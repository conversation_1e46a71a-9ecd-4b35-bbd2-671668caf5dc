import React, { useState, useEffect, useRef } from "react";
import * as Tabs from "@radix-ui/react-tabs";
import { TabsContent } from "@/components/ui/tabs";
import EmptyOrders from "./EmptyOrders";
import OrderDetail from "./OrderDetail";
import { GetOrderDetailsByUserId } from "@/services/ordersServices";
import { Loader } from "react-feather";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { getCurrencySymbol } from "@/services/currencyService";

interface OrderProps {
  userId: any;
  onOpenChange?: (open: boolean) => void;
  onDrawerChange?: (open: boolean) => void;
}
const Orders = ({ userId, onOpenChange, onDrawerChange }: OrderProps) => {
  // const  userId  = 'W5437xR475cbUigqs5BlqWStrZw1';
  const router = useRouter();
  const [selectedOrderItem, setSelectedOrderItem] = useState<string | null>(null);
  const [isOrderDetailOpen, setIsOrderDetailOpen] = useState(false);
  const [myOrders, setMyOrders] = useState<any[]>([]);
  const [receivedOrders, setReceivedOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const isFirstLoad = useRef(true);

  useEffect(() => {
    const fetchOrders = async () => {
      // Only set loading on initial load (userId change)
      if (isFirstLoad.current) {
        setLoading(true);
      }
      setError(null);
      try {
        if (!userId) {
          setError("User not logged in");
          setLoading(false);
          return;
        }
        const resp = await GetOrderDetailsByUserId({ userId });
        setMyOrders(resp.my_orders || []);
        setReceivedOrders(resp.received_orders || []);
      } catch (err: any) {
        setError("Failed to fetch orders");
        console.error("Error fetching orders:", err);
      } finally {
        setLoading(false);
        isFirstLoad.current = false; // After first load, don't show loading again
      }
    };
    fetchOrders();
  }, [userId, refreshKey]);

  const handleOrderDetailClose = (open: boolean) => {
    setIsOrderDetailOpen(open);
    if (!open) {
      setSelectedOrderItem(null);
    }
  };

  const generateFileUrl = (postFile: string | undefined): string | undefined => {
    const baseUrl = process.env.BASE_STORAGE_URL;
    if (!baseUrl) return undefined;

    if (!postFile) {
      return undefined;
    }

    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      return postFile;
    }

    return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  };

  const renderOrders = (orders: any[], emptyMsg: string, userId: string) => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-[calc(100vh-100px)]">
          <div className="flex flex-col items-center justify-center">
            <div className="relative">
              <Loader size={48} className="text-primary animate-spin" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-full"></div>
              </div>
            </div>
            <p className="text-sm text-gray-500">Loading Orders...</p>
          </div>
        </div>
      );
    }
    if (error) {
      return <div className="py-8 text-center text-red-500">{error}</div>;
    }
    if (!orders || orders.length === 0) {
      return <EmptyOrders message={emptyMsg} />;
    }
    const formatDate = (dateInput: any) => {
      let date: Date;
      if (
        dateInput &&
        typeof dateInput === "object" &&
        "seconds" in dateInput &&
        "nanoseconds" in dateInput
      ) {
        // Firestore Timestamp
        date = new Date(dateInput.seconds * 1000 + dateInput.nanoseconds / 1e6);
      } else {
        // ISO string or Date
        date = new Date(dateInput);
      }
      if (isNaN(date.getTime())) return "Invalid date";
      return format(date, "MMM d, yyyy");
    };
    return orders.map((order) => {
      const currencySymbol = getCurrencySymbol(order?.profileDetails?.currency) || "£";
      const currencyCode = order?.profileDetails?.currency || "GBP";
      const isMyOrder = order.userProfileId === userId;
      return (
        <div
          key={order.id}
          className={`w-full mt-3 rounded-md border p-4 cursor-pointer transition-all duration-200 ${
            selectedOrderItem === order.id?.toString()
              ? "border-primary bg-primary/5"
              : isMyOrder
                ? "border-primary/30"
                : "border-borderColor hover:border-primary/30"
          }`}
          onClick={() => {
            setSelectedOrderItem(order.id?.toString());
            setIsOrderDetailOpen(true);
          }}
        >
          <div className="flex justify-between">
            <div className="font-normal text-subtitle text-sm">
              Status{" "}
              <span
                className={`font-semibold uppercase text-sm text-primary ${
                  selectedOrderItem === order.id?.toString() ? "text-primary" : "text-subtitle"
                }`}
              >
                {order.status}
              </span>
            </div>
            <div className="text-subtitle font-normal text-sm">
              #{order.orderNumber || order.uniqueId || order.id}
            </div>
          </div>
          <div className="flex items-center gap-3 my-2">
            <div
              onClick={(e) => {
                e.stopPropagation();
                setIsOrderDetailOpen(false);
                setSelectedOrderItem(null);
                onOpenChange?.(false);
                onDrawerChange?.(false);
                // Navigate after state updates
                setTimeout(() => {
                  router.push(
                    `/profile/amuzn/${order.profileDetails.profile_name.replace(/\s+/g, "-")}`
                  );
                }, 100);
              }}
              className="cursor-pointer"
            >
              <img
                src={generateFileUrl(order.profileDetails?.avatar) || "/assets/profileAvatar.svg"}
                alt=""
                className="w-10 h-10 min-w-10 min-h-10 max-w-10 max-h-10 rounded-full"
              />
            </div>
            {order.profileDetails?.profile_name ? (
              <div
                className={`text-lg ${
                  selectedOrderItem === order.id?.toString()
                    ? "text-primary font-medium"
                    : "text-gray-900"
                }`}
              >
                {order.profileDetails.profile_name}
              </div>
            ) : (
              <p
                className={`text-lg ${
                  selectedOrderItem === order.id?.toString()
                    ? "text-primary font-medium"
                    : "text-gray-900"
                }`}
              >
                Unknown User
              </p>
            )}
          </div>
          <div>
            <div
              className={`text-lg cursor-pointer ${
                selectedOrderItem === order.id?.toString()
                  ? "text-primary font-medium"
                  : "text-gray-900"
              }`}
              onClick={(e) => {
                e.stopPropagation();
                setIsOrderDetailOpen(false);
                setSelectedOrderItem(null);
                onOpenChange?.(false);
                onDrawerChange?.(false);
                // Navigate after state updates
                setTimeout(() => {
                  router.push(
                    `/profile/amuzn/${order.profileDetails.profile_name.replace(/\s+/g, "-")}?view=Services`
                  );
                }, 100);
              }}
            >
              <p
                className={` font-bold my-2 text-sm ${
                  selectedOrderItem === order.id?.toString() ? "text-primary" : "text-gray-900"
                }`}
              >
                {order.title || order.serviceDetails?.title || "Order Title"}
              </p>
            </div>
          </div>
          <div className="flex justify-between ">
            <p className="text-subtitle text-sm">Total cost</p>
            <p
              className={` font-bold text-sm ${
                selectedOrderItem === order.id?.toString() ? "text-primary" : "text-gray-900"
              }`}
            >
              {(() => {
                // Calculate total price of selected customizations
                const selectedCustomizations =
                  order.serviceDetails?.customizations?.filter((option: any) =>
                    order.selectedCustomizations?.includes(option.id)
                  ) || [];
                const customizationsTotal = selectedCustomizations.reduce(
                  (sum: number, option: any) => {
                    const price = option.price !== undefined ? Number(option.price) : 0;
                    return sum + price;
                  },
                  0
                );
                const basePrice =
                  order?.serviceModel?.price !== undefined ? Number(order?.serviceModel.price) : 0;
                const subtotal = basePrice + customizationsTotal;
                const orderTotal = subtotal + subtotal * 0.04;
                return order.serviceDetails?.price !== undefined &&
                  !isNaN(Number(order.serviceDetails.price))
                  ? `${currencySymbol}${orderTotal.toFixed(2)}`
                  : "N/A";
              })()}
            </p>
          </div>
          <div className="flex justify-between">
            <p className="text-subtitle text-sm">Due date</p>
            <p
              className={` text-sm ${
                selectedOrderItem === order.id?.toString() ? "text-primary" : "text-gray-900"
              }`}
            >
              {formatDate(order.dueDate) || formatDate(order.specificDueDate) || "N/A"}
            </p>
          </div>
        </div>
      );
    });
  };

  // Combine all orders for the "All" tab
  const allOrders = [...myOrders, ...receivedOrders]?.sort((a, b) => b.added_at - a.added_at);

  // Find the selected order and its currency for the detail drawer
  const selectedOrder = allOrders.find((order) => order.id?.toString() === selectedOrderItem);
  const selectedCurrencySymbol = getCurrencySymbol(selectedOrder?.profileDetails?.currency) || "£";
  const selectedCurrencyCode = selectedOrder?.profileDetails?.currency || "GBP";

  // const refreshOrders = () => setRefreshKey((k) => k + 1);
  const refreshOrders = () => {
    setTimeout(() => {
      setRefreshKey((k) => k + 1);
    }, 1000);
  };

  return (
    <div className="w-full pr-4 ">
      <div className="sticky top-0 bg-white z-50 pb-2 overflow-hidden">
        <Tabs.Root defaultValue="all" className="w-full">
          <div className="TabsListBg w-full flex rounded-md bg-none p-1">
            <Tabs.List className="w-full flex h-[30px]" aria-label="Filter orders">
              <Tabs.Trigger
                className="TabsTriggerBg flex-1 py-0 rounded-md data-[state=active]:bg-[#BDBDBD] data-[state=active]:text-primary"
                value="all"
              >
                All
              </Tabs.Trigger>
              <Tabs.Trigger
                className="TabsTriggerBg flex-1 py-0 rounded-md data-[state=active]:bg-[#BDBDBD] data-[state=active]:text-primary"
                value="placed"
              >
                Placed
              </Tabs.Trigger>
              <Tabs.Trigger
                className="TabsTriggerBg flex-1 py-0 rounded-md data-[state=active]:bg-[#BDBDBD] data-[state=active]:text-primary"
                value="received"
              >
                Received
              </Tabs.Trigger>
            </Tabs.List>
          </div>

          <div className="pb-12 mt-4">
            <div className="overflow-y-scroll hide-scroll h-[calc(100vh-150px)] pb-12">
              <TabsContent value="all">
                {renderOrders(allOrders, "You don't have any orders yet", userId)}
              </TabsContent>
              <TabsContent value="placed">
                {renderOrders(myOrders, "You haven't placed any orders yet", userId)}
              </TabsContent>
              <TabsContent value="received">
                {renderOrders(receivedOrders, "You haven't received any orders yet", userId)}
              </TabsContent>
            </div>
          </div>
        </Tabs.Root>
      </div>

      <OrderDetail
        isOpen={isOrderDetailOpen}
        onOpenChange={handleOrderDetailClose}
        selectedOrderItem={selectedOrderItem}
        order={selectedOrder}
        currencySymbol={selectedCurrencySymbol}
        currencyCode={selectedCurrencyCode}
        userId={userId}
        refreshOrders={refreshOrders}
        onDrawerChange={onDrawerChange || (() => {})}
      />
    </div>
  );
};

export default Orders;
